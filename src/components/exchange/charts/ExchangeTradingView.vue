<template>
  <div class="exchange-tradingview-wrap">
    <div id="kline_container" ref="kline_container" class="kline_container"></div>
    <div
        v-if="Loading"
        v-loading="Loading"
        class="loadingBox"
      >
    </div>
  </div>
</template>
<script lang="ts" setup>
import { setStorage, getStorage } from '~/utils'
import jstz from 'jstz'
import MonoLoading from '~/components/common/icon-svg/MonoLoading.vue'
import useTradingView from '~/composables/useTradingView'
import useDatafeedAction from '~/composables/useDatafeedAction'
import * as widget1 from '~/public/tradingview/charting_library/charting_library'
console.log(333, widget1)
import { commonStore } from '~/stores/commonStore'
import { nextTick } from 'vue'
const store = commonStore()
const { pairInfo, isPairDetail, pair } = storeToRefs(store)
const custom_css_url = '/tradingview/tradingview_style/tradingview_custom.css'
const colorMode = useColorMode()
const { locale, t } = useI18n()
const klineNotLoad = ref(false)
const { option, tradingviewLangMap } = useTradingView(colorMode.preference, 'green-up')
const tradingViewOption = option[colorMode.preference]
const props = defineProps({
  pair: {
    type: String,
    default: ''
  },
  resolution: {
    type: String,
    default: '15m'
  },
  isShowTechnicalIndicator: {
    type: Boolean,
    default: false
  },
  isShowTradingViewSetting: {
    type: Boolean,
    default: false
  },
  isLoading: {
    type: Boolean,
    default: true
  }
})
import { ResolutionManager } from '~/composables/useDatafeedAction'
const emit = defineEmits(['closeTechnicalIndicator','closeTradingViewSetting'])
const widgetOption = {
  debug: false,
  symbol: props.pair || pair.value,
  timezone: 'Asia/Shanghai',
  container: 'kline_container',
  library_path: '/tradingview/charting_library/',
  custom_css_url,
  auto_save_delay: 0.001,
  datafeed: useDatafeedAction(pairInfo.value),
  interval: props.resolution ? ResolutionManager.getResolution(props.resolution) : '15',
  locale: tradingviewLangMap[locale.value] || locale.value,
  autosize: true,
  disabled_features: [
    "header_screenshot",
    "header_symbol_search",
    "header_undo_redo",
    "header_compare",
    "header_chart_type",
    "header_resolutions",
    "header_widget",
    "volume_force_overlay",
    "use_localstorage_for_settings",
    "symbol_search_hot_key",
    'timeframes_toolbar'
  ],
  enabled_features: [
    "keep_left_toolbar_visible_on_small_screens",
    "save_chart_properties_to_local_storage"
  ],
  toolbar_bg: 'transparent',
  ...tradingViewOption
} as any
let widget: any = null
let datafeedInstance: any = null

// 强制重置datafeed状态
window.resetTradingView = () => {
  if (datafeedInstance && datafeedInstance.forceReset) {
    datafeedInstance.forceReset()
  }
  // 同时重置组件状态
  isTransitioning.value = false
  Loading.value = false
}
// 紧急恢复函数，当发现数据不更新时可调用
window.emergencyRecovery = async () => {
  
  // 1. 重置所有状态
  isTransitioning.value = false
  Loading.value = false
  
  // 2. 重置datafeed
  if (datafeedInstance && datafeedInstance.forceReset) {
    datafeedInstance.forceReset()
  }
  
  // 3. 重新初始化widget
  if (widget) {
    try {
      widget.remove()
    } catch (e) {
    }
  }
  
  // 4. 等待一小段时间后重新初始化
  await new Promise(resolve => setTimeout(resolve, 500))
  await initChart()
}

// 手动触发数据更新
window.manualUpdateTradingView = () => {
  if (datafeedInstance && datafeedInstance.manualTriggerUpdate) {
    datafeedInstance.manualTriggerUpdate()
  }
}

// 1M数据调试函数
window.debug1MData = () => {
  if (datafeedInstance && datafeedInstance.getSubscriptionStatus) {
    const status = datafeedInstance.getSubscriptionStatus()
  }
  
  // 检查store中的1M数据
  const store = commonStore()
  const { klineList, klineTicker } = storeToRefs(store)
}

const Loading = ref(true)
const initChart = async () => {
  const currentPair = props.pair || pair.value

  if (!currentPair) {
    return
  }

  // 紧急修复：初始化时清理可能残留的状态，确保干净的开始
  if (datafeedInstance && datafeedInstance.forceReset) {
    datafeedInstance.forceReset()
  }

  // 直接初始化，无需延迟
  datafeedInstance = useDatafeedAction(pairInfo.value)
  widgetOption.datafeed = datafeedInstance
  widgetOption.symbol = currentPair
  widgetOption.interval = props.resolution ? ResolutionManager.getResolution(props.resolution) : '15',
  
  widget = new window.TradingView.widget(widgetOption)
  
  widget.onChartReady(() => {
    widget.activeChart().setChartType(1)
    widget.addCustomCSSFile(`/tradingview/tradingview_style/${colorMode.preference}.css`)
    Loading.value = false
  })
}

const previousResolution = ref(props.resolution)
const isTransitioning = ref(false)

watch(() => props.resolution, async (newVal, oldVal) => {
  // 只在真正切换中才阻止新的切换
  if (isTransitioning.value) {
    return
  }
  
  if (newVal && widget) {
    isTransitioning.value = true
    
    // 设置超时保护，防止isTransitioning永远为true
    const transitionTimeout = setTimeout(() => {
      if (isTransitioning.value) {
        isTransitioning.value = false
        Loading.value = false
      }
    }, 5000) // 5秒超时
    
    try {
      // 尝试使用TradingView原生API切换周期，避免重新创建widget
      const chart = widget.activeChart()
      const targetResolution = ResolutionManager.getResolution(newVal)
      
      // 优先使用原生API，避免widget重新创建导致的数据断档
      if (chart && chart.setResolution && newVal !== '1M') {
        // 在切换前预先设置loading状态，但不阻塞数据显示
        Loading.value = true
        // 同步更新datafeed状态，确保数据推送正确映射
        if (datafeedInstance) {
          // 轻度清理，仅取消不相关请求，保持当前数据流
          datafeedInstance.clearCache(true, widgetOption.symbol, newVal)
        }
        
        // 添加错误处理和超时保护
        let resolutionChanged = false
        const resolutionTimeout = setTimeout(() => {
          if (!resolutionChanged) {
            clearTimeout(transitionTimeout)
            Loading.value = false
            isTransitioning.value = false
          }
        }, 3000)
        
        try {
          chart.setResolution(targetResolution, () => {
            resolutionChanged = true
            clearTimeout(resolutionTimeout)
            clearTimeout(transitionTimeout)
            Loading.value = false
            isTransitioning.value = false
          })
        } catch (error) {
          resolutionChanged = true
          clearTimeout(resolutionTimeout)
          clearTimeout(transitionTimeout)
          Loading.value = false
          isTransitioning.value = false
          // 不return，继续执行widget重建
        }
        
        // 如果3秒内完成了切换，直接返回
        if (resolutionChanged) {
          return
        }
      }
    } catch (error) {
      // 确保在错误情况下也重置状态
      isTransitioning.value = false
      Loading.value = false
    }
    // 重要：在重建前先确保状态清理
    if (datafeedInstance && datafeedInstance.forceReset) {
      datafeedInstance.forceReset()
    }
    
    // 在删除旧widget前，先创建新的datafeed实例，确保数据准备就绪
    const newDatafeedInstance = useDatafeedAction(pairInfo.value)
    
    // 预先设置新的widget配置
    const newWidgetOption = {
      ...widgetOption,
      datafeed: newDatafeedInstance,
      symbol: props.pair || pair.value,
      interval: ResolutionManager.getResolution(newVal)
    }
    
    // 快速切换：先创建新widget再销毁旧widget，减少空档期
    const oldWidget = widget
    
    try {
      widget = new window.TradingView.widget(newWidgetOption)
      
      widget.onChartReady(() => {
        
        // 新widget就绪后再清理旧的，确保界面连续性
        if (oldWidget) {
          try {
            oldWidget.remove()
          } catch (e) {
            
          }
        }
        
        // 更新全局引用
        datafeedInstance = newDatafeedInstance
        widgetOption.datafeed = datafeedInstance
        widgetOption.symbol = newWidgetOption.symbol
        widgetOption.interval = newWidgetOption.interval
        
        widget.activeChart().setChartType(1)
        widget.addCustomCSSFile(`/tradingview/tradingview_style/${colorMode.preference}.css`)
        clearTimeout(transitionTimeout)
        Loading.value = false
        isTransitioning.value = false
      })
    } catch (error) {
      clearTimeout(transitionTimeout)
      Loading.value = false
      isTransitioning.value = false
      // 保留旧widget
      widget = oldWidget
    }
  }

  previousResolution.value = oldVal
}, { flush: 'post' }) // 使用post确保DOM更新后执行
const currentPairSymbol = computed(() => props.pair || pair.value)
const hasChanged = ref(false)

watch(() => currentPairSymbol.value, async (newVal, oldVal) => {
  if (newVal !== oldVal && newVal !== undefined && widget && !props.isLoading && !isTransitioning.value) {
    isTransitioning.value = true
    
    // 设置超时保护
    const transitionTimeout = setTimeout(() => {
      if (isTransitioning.value) {
        isTransitioning.value = false
        Loading.value = false
      }
    }, 5000)
    
    try {
      // 优先使用TradingView原生API切换币种，保持数据连续性
      const chart = widget.activeChart()
      if (chart && chart.setSymbol) {
        Loading.value = true
        
        // 预先更新datafeed状态以支持新币种
        if (datafeedInstance) {
          datafeedInstance.clearCache(true, newVal, props.resolution)
        }
        
        chart.setSymbol(newVal, () => {
          clearTimeout(transitionTimeout)
          Loading.value = false
          isTransitioning.value = false
        })
        return
      }
    } catch (error) {
      clearTimeout(transitionTimeout)
      isTransitioning.value = false
      Loading.value = false
    }
    
    // 创建新datafeed实例，预加载新币种数据
    const newDatafeedInstance = useDatafeedAction(pairInfo.value)
    
    const newWidgetOption = {
      ...widgetOption,
      datafeed: newDatafeedInstance,
      symbol: newVal,
      interval: props.resolution ? ResolutionManager.getResolution(props.resolution) : '15'
    }
    
    // 快速切换：先创建新widget再销毁旧widget
    const oldWidget = widget
    widget = new window.TradingView.widget(newWidgetOption)

    widget.onChartReady(() => {
      // 新widget就绪后清理旧的
      if (oldWidget) {
        try {
          oldWidget.remove()
        } catch (e) {

        }
      }
      
      // 更新全局引用
      datafeedInstance = newDatafeedInstance
      widgetOption.datafeed = datafeedInstance
      widgetOption.symbol = newVal
      widgetOption.interval = newWidgetOption.interval
      
      widget.activeChart().setChartType(1)
      widget.addCustomCSSFile(`/tradingview/tradingview_style/${colorMode.preference}.css`)
      clearTimeout(transitionTimeout)
      Loading.value = false
      isTransitioning.value = false
    })
  }
}, { flush: 'post' }) // 使用post确保DOM更新后执行

onMounted(() => {
  const currentPair = props.pair || pair.value
  if (currentPair && JSON.stringify(pairInfo.value) !== '{}') {
    initChart()
    hasChanged.value = true
  } else if (currentPair) {

  }
})

watch(() => pairInfo.value, (newPairInfo, oldPairInfo) => {
  const currentPair = props.pair || pair.value
  
  if (!hasChanged.value && currentPair && JSON.stringify(newPairInfo || {}) !== '{}') {
    hasChanged.value = true
    initChart()
  }
}, { immediate: true })

watch(() => colorMode.preference, (val) => {
  widget.changeTheme(colorMode.preference === 'light' ? 'Light' : 'Dark')
  setTimeout(() => {
    const options = useTradingView(val, 'green-up').option[val]
    widget.applyOverrides(options.overrides)
    widget.applyStudiesOverrides(options.studies_overrides)
  }, 10)
  widget.addCustomCSSFile(`/tradingview/tradingview_style/${val}.css`)
})
watch(() => props.isShowTechnicalIndicator, (TechnicalIndicator) => {
  if (TechnicalIndicator && widget) {
    widget.chart().executeActionById("insertIndicator")
    setTimeout(() => {
      emit('closeTechnicalIndicator')
    }, 100)
  }
}, { immediate: true })

watch(() => props.isShowTradingViewSetting, (Setting) => {
  if (Setting && widget) {
    widget.chart().executeActionById("chartProperties")
    setTimeout(() => {
      emit('closeTradingViewSetting')
    }, 100)
  }
}, { immediate: true })
</script>
<style lang="scss" scoped>  
.exchange-tradingview-wrap{
  width:100%;
  height:calc(100% - 46px);
  position:relative;
  .loadingBox {
    width: 100%;
    height: 100%;
    position: absolute;
    @include bg-color(bg-primary);
    z-index: 999;
    left: 0;
    top: 0;
  }
  .kline_container{
    background: transparent;
    width: 100%;
    height: calc(100%);
  }
}
@include mb{
  .exchange-tradingview-wrap{
    height:calc(100% - 44px);
  }
}
</style>